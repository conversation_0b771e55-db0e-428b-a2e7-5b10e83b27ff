# 测试环境 Docker Compose 覆盖配置
services:
  api:
    container_name: erp-api-test
    environment:
      - APP_ENV=testing
      - ERP_LOG_LEVEL=info
      - ERP_LOG_FORMAT=json
      - ERP_DB_NAME=erp_testing
      - ERP_REDIS_DB=2
    ports:
      - "8080:8080"
      - "9100:9100"
    volumes:
      - ./var/logs/test:/app/logs
      - ./var/uploads/test:/app/uploads
    restart: unless-stopped

  postgres:
    container_name: erp-postgres-test
    environment:
      POSTGRES_DB: erp_testing
      POSTGRES_USER: erp_test
      POSTGRES_PASSWORD: test_password_123
    ports:
      - "5433:5432"  # 避免与开发环境端口冲突
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
      - ./scripts/init-test-db.sql:/docker-entrypoint-initdb.d/init.sql

  redis:
    container_name: erp-redis-test
    ports:
      - "6380:6379"  # 避免与开发环境端口冲突
    volumes:
      - redis_test_data:/data
    command: >
      redis-server
      --appendonly yes
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru

  # Jaeger分布式追踪(测试环境)
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: erp-jaeger-test
    ports:
      - "16687:16686" # Jaeger UI
      - "14269:14268" # Jaeger HTTP collector
      - "4319:4317"   # OTLP gRPC receiver
      - "4320:4318"   # OTLP HTTP receiver
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - erp-network
    restart: unless-stopped

  # 测试工具
  test-runner:
    build:
      context: .
      dockerfile: Dockerfile
      target: builder
    container_name: erp-test-runner
    environment:
      - APP_ENV=testing
      - ERP_CONFIG_PATH=/app/configs
    volumes:
      - .:/app
      - test_cache:/go/pkg/mod
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - erp-network
    command: ["go", "test", "-v", "./..."]
    profiles:
      - test

  # 集成测试
  integration-test:
    build:
      context: .
      dockerfile: Dockerfile
      target: builder
    container_name: erp-integration-test
    environment:
      - APP_ENV=testing
      - ERP_CONFIG_PATH=/app/configs
    volumes:
      - .:/app
      - test_cache:/go/pkg/mod
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      api:
        condition: service_healthy
    networks:
      - erp-network
    command: ["go", "test", "-v", "./test/integration/..."]
    profiles:
      - integration

volumes:
  postgres_test_data:
    name: erp-postgres-test-data
  redis_test_data:
    name: erp-redis-test-data
  test_cache:
    name: erp-test-cache 