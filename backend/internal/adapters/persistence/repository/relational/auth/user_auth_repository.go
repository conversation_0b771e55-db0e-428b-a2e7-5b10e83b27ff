package security

import (
	"context"

	"backend/internal/adapters/persistence/repository/abstraction"
	"backend/internal/domain/auth/entity"
	"backend/internal/domain/auth/repository"
	"backend/pkg/adapters/database"
	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	dbAbstraction "backend/pkg/infrastructure/database/abstraction"
)

// UserAuthRepository 用户认证仓储实现
type UserAuthRepository struct {
	*abstraction.BaseRepository
}

// NewUserAuthRepository 创建用户认证仓储实例
func NewUserAuthRepository(dbManager dbAbstraction.Manager) repository.UserAuthRepository {
	return &UserAuthRepository{
		BaseRepository: abstraction.NewBaseRepository(dbManager),
	}
}

// TODO: 事务支持需要重新实现

// Save 创建用户认证记录
func (r *UserAuthRepository) Save(ctx context.Context, auth *entity.UserAuth) error {
	dataAccess := r.GetDataAccess()
	if err := dataAccess.Create(ctx, auth); err != nil {
		return database.TranslateDBError(err)
	}
	return nil
}

// Update 更新用户认证记录
func (r *UserAuthRepository) Update(ctx context.Context, auth *entity.UserAuth) error {
	dataAccess := r.GetDataAccess()
	if err := dataAccess.Update(ctx, auth); err != nil {
		return database.TranslateDBError(err)
	}
	return nil
}

// Delete 标记用户认证记录为已删除
func (r *UserAuthRepository) Delete(ctx context.Context, businessID string) error {
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("business_id = ?", businessID).
		Exec("DELETE FROM user_auths WHERE business_id = ?", businessID)

	if err != nil {
		if database.IsNotFoundError(err) {
			return apperrors.NewNotFound(codes.ResourceNotFound, "用户认证记录不存在").
				WithDetail("business_id", businessID).
				Wrap(err).Build()
		}
		return database.TranslateDBError(err)
	}
	return nil
}

// BatchCreate 在单个事务中创建多个用户认证记录
func (r *UserAuthRepository) BatchCreate(ctx context.Context, auths []*entity.UserAuth) error {
	dataAccess := r.GetDataAccess()
	for _, auth := range auths {
		if err := dataAccess.Create(ctx, auth); err != nil {
			return database.TranslateDBError(err)
		}
	}
	return nil
}

// FindByIdentifier 通过身份类型和唯一标识查找认证实体
func (r *UserAuthRepository) FindByIdentifier(ctx context.Context, identityType, identifier string) (*entity.UserAuth, error) {
	var auth entity.UserAuth
	dataAccess := r.GetDataAccess()

	err := dataAccess.First(ctx, &auth, "identity_type = ? AND identifier = ?", identityType, identifier)
	if err != nil {
		if database.IsNotFoundError(err) {
			return nil, apperrors.NewNotFound(codes.ResourceNotFound, "用户认证记录不存在").
				WithDetail("identity_type", identityType).
				WithDetail("identifier", identifier).
				Wrap(err).Build()
		}
		return nil, database.TranslateDBError(err)
	}
	return &auth, nil
}

// FindByBusinessID 通过业务ID查找认证实体
func (r *UserAuthRepository) FindByBusinessID(ctx context.Context, businessID string) (*entity.UserAuth, error) {
	var auth entity.UserAuth
	dataAccess := r.GetDataAccess()

	err := dataAccess.First(ctx, &auth, "business_id = ?", businessID)
	if err != nil {
		if database.IsNotFoundError(err) {
			return nil, apperrors.NewNotFound(codes.ResourceNotFound, "用户认证记录不存在").
				WithDetail("business_id", businessID).
				Wrap(err).Build()
		}
		return nil, database.TranslateDBError(err)
	}
	return &auth, nil
}

// FindByUserBusinessID 通过用户业务ID查找所有认证实体
func (r *UserAuthRepository) FindByUserBusinessID(ctx context.Context, userBusinessID string) ([]*entity.UserAuth, error) {
	var auths []*entity.UserAuth
	dataAccess := r.GetDataAccess()

	err := dataAccess.Find(ctx, &auths, "user_business_id = ?", userBusinessID)
	if err != nil {
		return nil, database.TranslateDBError(err)
	}
	return auths, nil
}

// ExistsByIdentifier 检查指定类型的标识符是否已存在
func (r *UserAuthRepository) ExistsByIdentifier(ctx context.Context, identityType, identifier string) (bool, error) {
	dataAccess := r.GetDataAccess()
	exists, err := dataAccess.Exists(ctx, &entity.UserAuth{}, "identity_type = ? AND identifier = ?", identityType, identifier)
	if err != nil {
		return false, database.TranslateDBError(err)
	}
	return exists, nil
}

// FindByTechID 通过技术ID查找认证实体
func (r *UserAuthRepository) FindByTechID(ctx context.Context, techID int64) (*entity.UserAuth, error) {
	var auth entity.UserAuth
	dataAccess := r.GetDataAccess()

	err := dataAccess.First(ctx, &auth, "id = ? AND deleted_at IS NULL", techID)
	if err != nil {
		if database.IsNotFoundError(err) {
			return nil, apperrors.NewNotFound(codes.ResourceNotFound, "用户认证记录不存在").
				WithDetail("tech_id", techID).
				Wrap(err).Build()
		}
		return nil, database.TranslateDBError(err)
	}
	return &auth, nil
}

// FindByUsername 通过用户名查找认证实体
func (r *UserAuthRepository) FindByUsername(ctx context.Context, tenantID, username string) (*entity.UserAuth, error) {
	var userAuth entity.UserAuth
	dataAccess := r.GetDataAccess()

	err := dataAccess.First(ctx, &userAuth, "tenant_id = ? AND username = ? AND is_deleted = false", tenantID, username)
	if err != nil {
		if database.IsNotFoundError(err) {
			return nil, apperrors.NewNotFound(codes.ResourceNotFound, "用户认证记录不存在").
				WithDetail("tenant_id", tenantID).
				WithDetail("username", username).
				Wrap(err).Build()
		}
		return nil, database.TranslateDBError(err)
	}
	return &userAuth, nil
}

// FindByEmail 通过邮箱查找认证实体
func (r *UserAuthRepository) FindByEmail(ctx context.Context, tenantID, email string) (*entity.UserAuth, error) {
	var userAuth entity.UserAuth
	dataAccess := r.GetDataAccess()

	err := dataAccess.First(ctx, &userAuth, "tenant_id = ? AND email = ? AND is_deleted = false", tenantID, email)
	if err != nil {
		if database.IsNotFoundError(err) {
			return nil, apperrors.NewNotFound(codes.ResourceNotFound, "用户认证记录不存在").
				WithDetail("tenant_id", tenantID).
				WithDetail("email", email).
				Wrap(err).Build()
		}
		return nil, database.TranslateDBError(err)
	}
	return &userAuth, nil
}

// FindByUserID 通过用户ID查找认证实体
func (r *UserAuthRepository) FindByUserID(ctx context.Context, userID string) (*entity.UserAuth, error) {
	var userAuth entity.UserAuth
	dataAccess := r.GetDataAccess()

	err := dataAccess.First(ctx, &userAuth, "user_id = ? AND is_deleted = false", userID)
	if err != nil {
		if database.IsNotFoundError(err) {
			return nil, apperrors.NewNotFound(codes.ResourceNotFound, "用户认证记录不存在").
				WithDetail("user_id", userID).
				Wrap(err).Build()
		}
		return nil, database.TranslateDBError(err)
	}
	return &userAuth, nil
}

// TODO: 以下方法需要重新实现以使用database.Manager的抽象接口

// FindByTenantID 通过租户ID查找认证实体
func (r *UserAuthRepository) FindByTenantID(ctx context.Context, tenantID string, limit, offset int) ([]*entity.UserAuth, int64, error) {
	// TODO: 实现分页查询
	return []*entity.UserAuth{}, 0, nil
}

// FindActiveByUsername 查找活跃的用户认证信息（全局）认证实体
func (r *UserAuthRepository) FindActiveByUsername(ctx context.Context, username string) (*entity.UserAuth, error) {
	// TODO: 实现活跃用户查询
	return nil, apperrors.NewNotFound(codes.ResourceNotFound, "用户认证记录不存在").Build()
}

// FindActiveByUsernameInTenant 查找指定租户下活跃的用户认证信息（通过用户名）认证实体
func (r *UserAuthRepository) FindActiveByUsernameInTenant(ctx context.Context, tenantID, username string) (*entity.UserAuth, error) {
	// TODO: 实现租户内活跃用户查询
	return nil, apperrors.NewNotFound(codes.ResourceNotFound, "用户认证记录不存在").Build()
}

// FindActiveByEmail 查找活跃的用户认证信息（通过邮箱）认证实体
func (r *UserAuthRepository) FindActiveByEmail(ctx context.Context, tenantID, email string) (*entity.UserAuth, error) {
	// TODO: 实现通过邮箱查找活跃用户
	return nil, apperrors.NewNotFound(codes.ResourceNotFound, "用户认证记录不存在").Build()
}

// FindByUserIDs 批量查找用户认证信息认证实体
func (r *UserAuthRepository) FindByUserIDs(ctx context.Context, userIDs []string) ([]*entity.UserAuth, error) {
	// TODO: 实现批量查询
	return []*entity.UserAuth{}, nil
}

// FindByTenantIDs 批量查找租户的用户认证信息认证实体
func (r *UserAuthRepository) FindByTenantIDs(ctx context.Context, tenantIDs []string) ([]*entity.UserAuth, error) {
	// TODO: 实现批量租户查询
	return []*entity.UserAuth{}, nil
}

// CountByTenantID 统计租户下的用户认证数量认证实体
func (r *UserAuthRepository) CountByTenantID(ctx context.Context, tenantID string) (int64, error) {
	// TODO: 实现统计功能
	return 0, nil
}

// CountActiveByTenantID 统计租户下的活跃用户认证数量认证实体
func (r *UserAuthRepository) CountActiveByTenantID(ctx context.Context, tenantID string) (int64, error) {
	// TODO: 实现活跃用户统计
	return 0, nil
}
