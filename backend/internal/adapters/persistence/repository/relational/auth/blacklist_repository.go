package security

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"

	"backend/internal/domain/auth/entity"
	"backend/internal/domain/auth/repository"
	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
)

// BlacklistRepository Token黑名单仓储的数据库实现
type BlacklistRepository struct {
	db *gorm.DB
}

// NewBlacklistRepository 创建新的Token黑名单仓储实例
func NewBlacklistRepository(db *gorm.DB) repository.BlacklistRepository {
	return &BlacklistRepository{db: db}
}

// === 基础CRUD操作 ===

// Save 保存Token黑名单条目
func (r *BlacklistRepository) Save(ctx context.Context, entry *entity.TokenBlacklistEntry) error {
	if err := r.db.WithContext(ctx).Create(entry).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseConnection, "保存Token黑名单条目失败").Wrap(err).Build()
	}
	return nil
}

// Update 更新Token黑名单条目
func (r *BlacklistRepository) Update(ctx context.Context, entry *entity.TokenBlacklistEntry) error {
	result := r.db.WithContext(ctx).Save(entry)
	if result.Error != nil {
		return apperrors.NewInternal(codes.DatabaseConnection, "更新Token黑名单条目失败").Wrap(result.Error).Build()
	}
	if result.RowsAffected == 0 {
		return apperrors.NewInternal(codes.DatabaseNotFound, "Token黑名单条目").Wrap(fmt.Errorf("Token ID: %s", entry.TokenID)).Build()
	}
	return nil
}

// Delete 删除Token黑名单条目
func (r *BlacklistRepository) Delete(ctx context.Context, tokenID string) error {
	result := r.db.WithContext(ctx).Where("token_id = ?", tokenID).Delete(&entity.TokenBlacklistEntry{})
	if result.Error != nil {
		return apperrors.NewInternal(codes.DatabaseConnection, "删除Token黑名单条目失败").Wrap(result.Error).Build()
	}
	if result.RowsAffected == 0 {
		return apperrors.NewInternal(codes.DatabaseNotFound, "Token黑名单条目").Wrap(fmt.Errorf("Token ID: %s", tokenID)).Build()
	}
	return nil
}

// SaveWithTx 在事务中保存Token黑名单条目
func (r *BlacklistRepository) SaveWithTx(ctx context.Context, tx *gorm.DB, entry *entity.TokenBlacklistEntry) error {
	if err := tx.WithContext(ctx).Create(entry).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseConnection, "在事务中保存Token黑名单条目失败").Wrap(err).Build()
	}
	return nil
}

// === 查询操作 ===

// FindByTokenID 通过TokenID查找黑名单条目
func (r *BlacklistRepository) FindByTokenID(ctx context.Context, tokenID string) (*entity.TokenBlacklistEntry, error) {
	var entry entity.TokenBlacklistEntry
	err := r.db.WithContext(ctx).Where("token_id = ?", tokenID).First(&entry).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewInternal(codes.DatabaseNotFound, "Token黑名单条目").Wrap(fmt.Errorf("Token ID: %s", tokenID)).Build()
		}
		return nil, apperrors.NewInternal(codes.DatabaseConnection, "查找Token黑名单条目失败").Wrap(err).Build()
	}
	return &entry, nil
}

// FindByTokenHash 通过Token哈希查找黑名单条目
func (r *BlacklistRepository) FindByTokenHash(ctx context.Context, tokenHash string) (*entity.TokenBlacklistEntry, error) {
	var entry entity.TokenBlacklistEntry
	err := r.db.WithContext(ctx).Where("token_hash = ?", tokenHash).First(&entry).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewInternal(codes.DatabaseNotFound, "Token黑名单条目").Wrap(fmt.Errorf("Token哈希: %s", tokenHash)).Build()
		}
		return nil, apperrors.NewInternal(codes.DatabaseConnection, "查找Token黑名单条目失败").Wrap(err).Build()
	}
	return &entry, nil
}

// FindByUserID 通过用户ID查找黑名单条目列表
func (r *BlacklistRepository) FindByUserID(ctx context.Context, userID string) ([]*entity.TokenBlacklistEntry, error) {
	var entries []*entity.TokenBlacklistEntry
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).Order("blacklisted_at DESC").Find(&entries).Error
	if err != nil {
		return nil, apperrors.NewInternal(codes.DatabaseConnection, "查找Token黑名单条目失败").Wrap(err).Build()
	}
	return entries, nil
}

// FindBySessionID 通过会话ID查找黑名单条目列表
func (r *BlacklistRepository) FindBySessionID(ctx context.Context, sessionID string) ([]*entity.TokenBlacklistEntry, error) {
	var entries []*entity.TokenBlacklistEntry
	err := r.db.WithContext(ctx).Where("session_id = ?", sessionID).Order("blacklisted_at DESC").Find(&entries).Error
	if err != nil {
		return nil, apperrors.NewInternal(codes.DatabaseConnection, "查找Token黑名单条目失败").Wrap(err).Build()
	}
	return entries, nil
}

// === 批量操作 ===

// BatchCreate 批量创建黑名单条目
func (r *BlacklistRepository) BatchCreate(ctx context.Context, entries []*entity.TokenBlacklistEntry) error {
	if len(entries) == 0 {
		return nil
	}

	if err := r.db.WithContext(ctx).CreateInBatches(entries, 100).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseConnection, "批量创建Token黑名单条目失败").Wrap(err).Build()
	}
	return nil
}

// BatchDelete 批量删除黑名单条目
func (r *BlacklistRepository) BatchDelete(ctx context.Context, tokenIDs []string) error {
	if len(tokenIDs) == 0 {
		return nil
	}

	result := r.db.WithContext(ctx).Where("token_id IN ?", tokenIDs).Delete(&entity.TokenBlacklistEntry{})
	if result.Error != nil {
		return apperrors.NewInternal(codes.DatabaseConnection, "批量删除Token黑名单条目失败").Wrap(result.Error).Build()
	}
	return nil
}

// BlacklistTokensByUser 将用户的所有Token加入黑名单
func (r *BlacklistRepository) BlacklistTokensByUser(ctx context.Context, userID string, reason string) error {
	// 这需要与TokenRepository配合，先查询用户的Token，然后创建黑名单条目
	// 这里只是一个简化的实现
	// 假设我们有一个方法来获取用户的所有活跃Token
	// 实际实现中需要调用TokenRepository来获取Token列表
	// 然后为每个Token创建黑名单条目

	return nil // 简化实现
}

// BlacklistTokensBySession 将会话的所有Token加入黑名单
func (r *BlacklistRepository) BlacklistTokensBySession(ctx context.Context, sessionID string, reason string) error {
	// 类似于BlacklistTokensByUser的实现
	return nil // 简化实现
}

// === 过期清理 ===

// DeleteExpiredEntries 删除过期的黑名单条目
func (r *BlacklistRepository) DeleteExpiredEntries(ctx context.Context, before time.Time) (int64, error) {
	result := r.db.WithContext(ctx).
		Where("expires_at < ?", before).
		Delete(&entity.TokenBlacklistEntry{})

	if result.Error != nil {
		return 0, apperrors.NewInternal(codes.DatabaseConnection, "删除过期的Token黑名单条目失败").Wrap(result.Error).Build()
	}
	return result.RowsAffected, nil
}

// FindExpiredEntries 查找过期的黑名单条目
func (r *BlacklistRepository) FindExpiredEntries(ctx context.Context, before time.Time, limit int) ([]*entity.TokenBlacklistEntry, error) {
	var entries []*entity.TokenBlacklistEntry
	err := r.db.WithContext(ctx).
		Where("expires_at < ?", before).
		Limit(limit).
		Find(&entries).Error

	if err != nil {
		return nil, apperrors.NewInternal(codes.DatabaseConnection, "查找过期的Token黑名单条目失败").Wrap(err).Build()
	}
	return entries, nil
}

// === 验证操作 ===

// IsTokenBlacklisted 检查Token是否在黑名单中
func (r *BlacklistRepository) IsTokenBlacklisted(ctx context.Context, tokenID string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.TokenBlacklistEntry{}).
		Where("token_id = ? AND expires_at > ?", tokenID, time.Now()).
		Count(&count).Error

	if err != nil {
		return false, apperrors.NewInternal(codes.DatabaseConnection, "检查Token是否在黑名单中失败").Wrap(err).Build()
	}
	return count > 0, nil
}

// IsTokenHashBlacklisted 检查Token哈希是否在黑名单中
func (r *BlacklistRepository) IsTokenHashBlacklisted(ctx context.Context, tokenHash string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.TokenBlacklistEntry{}).
		Where("token_hash = ? AND expires_at > ?", tokenHash, time.Now()).
		Count(&count).Error

	if err != nil {
		return false, apperrors.NewInternal(codes.DatabaseConnection, "检查Token哈希是否在黑名单中失败").Wrap(err).Build()
	}
	return count > 0, nil
}

// ExistsByTokenID 检查黑名单条目是否存在
func (r *BlacklistRepository) ExistsByTokenID(ctx context.Context, tokenID string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.TokenBlacklistEntry{}).
		Where("token_id = ?", tokenID).
		Count(&count).Error

	if err != nil {
		return false, apperrors.NewInternal(codes.DatabaseConnection, "检查黑名单条目是否存在失败").Wrap(err).Build()
	}
	return count > 0, nil
}

// === 统计查询 ===

// CountByUser 统计用户的黑名单条目数量
func (r *BlacklistRepository) CountByUser(ctx context.Context, userID string) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.TokenBlacklistEntry{}).
		Where("user_id = ?", userID).
		Count(&count).Error

	if err != nil {
		return 0, apperrors.NewInternal(codes.DatabaseConnection, "统计用户的黑名单条目数量失败").Wrap(err).Build()
	}
	return count, nil
}

// CountByTokenType 统计指定Token类型的黑名单条目数量
func (r *BlacklistRepository) CountByTokenType(ctx context.Context, tokenType entity.TokenType) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.TokenBlacklistEntry{}).
		Where("token_type = ?", tokenType).
		Count(&count).Error

	if err != nil {
		return 0, apperrors.NewInternal(codes.DatabaseConnection, "统计指定Token类型的黑名单条目数量失败").Wrap(err).Build()
	}
	return count, nil
}

// CountActive 统计活跃的黑名单条目数量
func (r *BlacklistRepository) CountActive(ctx context.Context) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.TokenBlacklistEntry{}).
		Where("expires_at > ?", time.Now()).
		Count(&count).Error

	if err != nil {
		return 0, apperrors.NewInternal(codes.DatabaseConnection, "统计活跃的黑名单条目数量失败").Wrap(err).Build()
	}
	return count, nil
}
