package menu

import (
	"context"
	"fmt"
	"strings"

	"backend/internal/domain/menu/entity"
	"backend/internal/domain/menu/repository"
	"backend/internal/domain/menu/valueobject"
	"backend/pkg/adapters/database"
	"backend/pkg/infrastructure/logger"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// menuRepository PostgreSQL菜单仓储实现
type menuRepository struct {
	db     *gorm.DB
	logger logger.Logger
}

// NewMenuRepository 创建菜单仓储
func NewMenuRepository(db *gorm.DB, logger logger.Logger) repository.MenuRepository {
	return &menuRepository{
		db:     db,
		logger: logger,
	}
}

// Create 创建菜单
func (r *menuRepository) Create(ctx context.Context, menu *entity.Menu) error {
	if err := menu.Validate(); err != nil {
		return fmt.Errorf("菜单验证失败: %w", err)
	}

	// 生成ID（如果尚未生成）
	if menu.BusinessID == "" {
		menu.BusinessID = uuid.New().String()
	}

	if err := r.db.WithContext(ctx).Create(menu).Error; err != nil {
		return database.TranslateDBError(err)
	}

	r.logger.Info(ctx, "Menu created successfully", map[string]interface{}{
		"menu_id":   menu.BusinessID,
		"tenant_id": menu.TenantID,
		"name":      menu.Name,
	})

	return nil
}

// Update 更新菜单
func (r *menuRepository) Update(ctx context.Context, menu *entity.Menu) error {
	if err := menu.Validate(); err != nil {
		return fmt.Errorf("菜单验证失败: %w", err)
	}

	result := r.db.WithContext(ctx).Save(menu)
	if result.Error != nil {
		return database.TranslateDBError(result.Error)
	}

	if result.RowsAffected == 0 {
		return database.TranslateDBError(gorm.ErrRecordNotFound)
	}

	r.logger.Info(ctx, "Menu updated successfully", map[string]interface{}{
		"menu_id":   menu.BusinessID,
		"tenant_id": menu.TenantID,
	})

	return nil
}

// Delete 删除菜单（软删除）
func (r *menuRepository) Delete(ctx context.Context, id string) error {
	result := r.db.WithContext(ctx).Where("business_id = ?", id).Delete(&entity.Menu{})
	if result.Error != nil {
		return database.TranslateDBError(result.Error)
	}

	if result.RowsAffected == 0 {
		return database.TranslateDBError(gorm.ErrRecordNotFound)
	}

	r.logger.Info(ctx, "Menu deleted successfully", map[string]interface{}{
		"menu_id": id,
	})

	return nil
}

// FindByID 根据技术ID查找菜单
func (r *menuRepository) FindByID(ctx context.Context, id string) (*entity.Menu, error) {
	var menu entity.Menu
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&menu).Error; err != nil {
		return nil, database.TranslateDBError(err)
	}
	return &menu, nil
}

// FindByBusinessID 根据业务ID查找菜单
func (r *menuRepository) FindByBusinessID(ctx context.Context, businessID string) (*entity.Menu, error) {
	var menu entity.Menu
	if err := r.db.WithContext(ctx).Where("business_id = ?", businessID).First(&menu).Error; err != nil {
		return nil, database.TranslateDBError(err)
	}
	return &menu, nil
}

// FindByTenantID 根据租户ID查找所有菜单
func (r *menuRepository) FindByTenantID(ctx context.Context, tenantID string) ([]*entity.Menu, error) {
	var menus []*entity.Menu
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ?", tenantID).
		Order("level ASC, sort ASC, name ASC").
		Find(&menus).Error; err != nil {
		return nil, database.TranslateDBError(err)
	}
	return menus, nil
}

// FindByParentID 根据父菜单ID查找子菜单
func (r *menuRepository) FindByParentID(ctx context.Context, tenantID, parentID string) ([]*entity.Menu, error) {
	var menus []*entity.Menu
	query := r.db.WithContext(ctx).Where("tenant_id = ?", tenantID)

	if parentID == "" {
		query = query.Where("parent_id IS NULL OR parent_id = ''")
	} else {
		query = query.Where("parent_id = ?", parentID)
	}

	if err := query.Order("sort ASC, name ASC").Find(&menus).Error; err != nil {
		return nil, database.TranslateDBError(err)
	}
	return menus, nil
}

// FindRootMenus 查找根菜单
func (r *menuRepository) FindRootMenus(ctx context.Context, tenantID string) ([]*entity.Menu, error) {
	return r.FindByParentID(ctx, tenantID, "")
}

// FindByLevel 根据层级查找菜单
func (r *menuRepository) FindByLevel(ctx context.Context, tenantID string, level int) ([]*entity.Menu, error) {
	var menus []*entity.Menu
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND level = ?", tenantID, level).
		Order("sort ASC, name ASC").
		Find(&menus).Error; err != nil {
		return nil, database.TranslateDBError(err)
	}
	return menus, nil
}

// FindByPermissions 根据权限列表查找菜单
func (r *menuRepository) FindByPermissions(ctx context.Context, tenantID string, permissions []string) ([]*entity.Menu, error) {
	if len(permissions) == 0 {
		return []*entity.Menu{}, nil
	}

	var menus []*entity.Menu
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND (permission IN ? OR permission IS NULL OR permission = '')", tenantID, permissions).
		Order("level ASC, sort ASC, name ASC").
		Find(&menus).Error; err != nil {
		return nil, database.TranslateDBError(err)
	}
	return menus, nil
}

// FindByResourceAction 根据资源和操作查找菜单
func (r *menuRepository) FindByResourceAction(ctx context.Context, tenantID, resource, action string) ([]*entity.Menu, error) {
	var menus []*entity.Menu
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND resource = ? AND action = ?", tenantID, resource, action).
		Order("level ASC, sort ASC, name ASC").
		Find(&menus).Error; err != nil {
		return nil, database.TranslateDBError(err)
	}
	return menus, nil
}

// FindVisibleMenus 查找可见菜单
func (r *menuRepository) FindVisibleMenus(ctx context.Context, tenantID string) ([]*entity.Menu, error) {
	var menus []*entity.Menu
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND is_visible = ?", tenantID, true).
		Order("level ASC, sort ASC, name ASC").
		Find(&menus).Error; err != nil {
		return nil, database.TranslateDBError(err)
	}
	return menus, nil
}

// FindActiveMenus 查找激活菜单
func (r *menuRepository) FindActiveMenus(ctx context.Context, tenantID string) ([]*entity.Menu, error) {
	var menus []*entity.Menu
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND status = ?", tenantID, valueobject.MenuStatusActive).
		Order("level ASC, sort ASC, name ASC").
		Find(&menus).Error; err != nil {
		return nil, database.TranslateDBError(err)
	}
	return menus, nil
}

// FindByType 根据菜单类型查找菜单
func (r *menuRepository) FindByType(ctx context.Context, tenantID string, menuType valueobject.MenuType) ([]*entity.Menu, error) {
	var menus []*entity.Menu
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND menu_type = ?", tenantID, menuType).
		Order("level ASC, sort ASC, name ASC").
		Find(&menus).Error; err != nil {
		return nil, database.TranslateDBError(err)
	}
	return menus, nil
}

// FindDirectories 查找目录类型菜单
func (r *menuRepository) FindDirectories(ctx context.Context, tenantID string) ([]*entity.Menu, error) {
	return r.FindByType(ctx, tenantID, valueobject.MenuTypeDirectory)
}

// FindMenuItems 查找菜单类型菜单
func (r *menuRepository) FindMenuItems(ctx context.Context, tenantID string) ([]*entity.Menu, error) {
	return r.FindByType(ctx, tenantID, valueobject.MenuTypeMenu)
}

// FindButtons 查找按钮类型菜单
func (r *menuRepository) FindButtons(ctx context.Context, tenantID string) ([]*entity.Menu, error) {
	return r.FindByType(ctx, tenantID, valueobject.MenuTypeButton)
}

// FindMenuTree 查找菜单树
func (r *menuRepository) FindMenuTree(ctx context.Context, tenantID string) ([]*entity.Menu, error) {
	return r.FindByTenantID(ctx, tenantID)
}

// FindMenuTreeByParent 根据父菜单查找菜单树
func (r *menuRepository) FindMenuTreeByParent(ctx context.Context, tenantID, parentID string) ([]*entity.Menu, error) {
	var menus []*entity.Menu

	// 使用递归CTE查询子菜单树
	query := `
		WITH RECURSIVE menu_tree AS (
			-- 基础查询：查找指定父菜单的直接子菜单
			SELECT * FROM menus 
			WHERE tenant_id = ? AND parent_id = ? AND deleted_at IS NULL
			
			UNION ALL
			
			-- 递归查询：查找子菜单的子菜单
			SELECT m.* FROM menus m
			INNER JOIN menu_tree mt ON m.parent_id = mt.business_id
			WHERE m.tenant_id = ? AND m.deleted_at IS NULL
		)
		SELECT * FROM menu_tree ORDER BY level ASC, sort ASC, name ASC
	`

	if err := r.db.WithContext(ctx).Raw(query, tenantID, parentID, tenantID).Scan(&menus).Error; err != nil {
		return nil, database.TranslateDBError(err)
	}

	return menus, nil
}

// FindChildrenRecursive 递归查找所有子菜单
func (r *menuRepository) FindChildrenRecursive(ctx context.Context, tenantID, parentID string) ([]*entity.Menu, error) {
	return r.FindMenuTreeByParent(ctx, tenantID, parentID)
}

// FindWithSort 带排序的查找菜单
func (r *menuRepository) FindWithSort(ctx context.Context, tenantID string, orderBy string, limit, offset int) ([]*entity.Menu, error) {
	var menus []*entity.Menu

	// 验证排序字段
	validOrderFields := map[string]bool{
		"sort":       true,
		"level":      true,
		"name":       true,
		"created_at": true,
		"updated_at": true,
	}

	if !validOrderFields[strings.ToLower(orderBy)] {
		orderBy = "sort ASC, name ASC"
	}

	query := r.db.WithContext(ctx).Where("tenant_id = ?", tenantID).Order(orderBy)

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&menus).Error; err != nil {
		return nil, database.TranslateDBError(err)
	}

	return menus, nil
}

// FindByNamePattern 根据名称模式查找菜单
func (r *menuRepository) FindByNamePattern(ctx context.Context, tenantID, pattern string) ([]*entity.Menu, error) {
	var menus []*entity.Menu
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND name ILIKE ?", tenantID, "%"+pattern+"%").
		Order("level ASC, sort ASC, name ASC").
		Find(&menus).Error; err != nil {
		return nil, database.TranslateDBError(err)
	}
	return menus, nil
}

// FindByPathPattern 根据路径模式查找菜单
func (r *menuRepository) FindByPathPattern(ctx context.Context, tenantID, pattern string) ([]*entity.Menu, error) {
	var menus []*entity.Menu
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND path ILIKE ?", tenantID, "%"+pattern+"%").
		Order("level ASC, sort ASC, name ASC").
		Find(&menus).Error; err != nil {
		return nil, database.TranslateDBError(err)
	}
	return menus, nil
}

// CreateBatch 批量创建菜单
func (r *menuRepository) CreateBatch(ctx context.Context, menus []*entity.Menu) error {
	if len(menus) == 0 {
		return nil
	}

	// 验证所有菜单并生成ID
	for _, menu := range menus {
		if err := menu.Validate(); err != nil {
			return fmt.Errorf("菜单验证失败: %w", err)
		}
		// 生成ID（如果尚未生成）
		if menu.BusinessID == "" {
			menu.BusinessID = uuid.New().String()
		}
	}

	if err := r.db.WithContext(ctx).CreateInBatches(menus, 100).Error; err != nil {
		return database.TranslateDBError(err)
	}

	r.logger.Info(ctx, "Menus created in batch", map[string]interface{}{
		"count": len(menus),
	})

	return nil
}

// UpdateBatch 批量更新菜单
func (r *menuRepository) UpdateBatch(ctx context.Context, menus []*entity.Menu) error {
	if len(menus) == 0 {
		return nil
	}

	// 验证所有菜单
	for _, menu := range menus {
		if err := menu.Validate(); err != nil {
			return fmt.Errorf("菜单验证失败: %w", err)
		}
	}

	// 使用事务批量更新
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, menu := range menus {
			if err := tx.Save(menu).Error; err != nil {
				return database.TranslateDBError(err)
			}
		}
		return nil
	})
}

// DeleteBatch 批量删除菜单
func (r *menuRepository) DeleteBatch(ctx context.Context, ids []string) error {
	if len(ids) == 0 {
		return nil
	}

	result := r.db.WithContext(ctx).Where("business_id IN ?", ids).Delete(&entity.Menu{})
	if result.Error != nil {
		return database.TranslateDBError(result.Error)
	}

	r.logger.Info(ctx, "Menus deleted in batch", map[string]interface{}{
		"count": result.RowsAffected,
		"ids":   ids,
	})

	return nil
}

// CountByTenant 统计租户菜单数量
func (r *menuRepository) CountByTenant(ctx context.Context, tenantID string) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&entity.Menu{}).Where("tenant_id = ?", tenantID).Count(&count).Error; err != nil {
		return 0, database.TranslateDBError(err)
	}
	return count, nil
}

// CountByParent 统计父菜单下的子菜单数量
func (r *menuRepository) CountByParent(ctx context.Context, tenantID, parentID string) (int64, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&entity.Menu{}).Where("tenant_id = ?", tenantID)

	if parentID == "" {
		query = query.Where("parent_id IS NULL OR parent_id = ''")
	} else {
		query = query.Where("parent_id = ?", parentID)
	}

	if err := query.Count(&count).Error; err != nil {
		return 0, database.TranslateDBError(err)
	}
	return count, nil
}

// CountByType 统计指定类型的菜单数量
func (r *menuRepository) CountByType(ctx context.Context, tenantID string, menuType valueobject.MenuType) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&entity.Menu{}).
		Where("tenant_id = ? AND menu_type = ?", tenantID, menuType).
		Count(&count).Error; err != nil {
		return 0, database.TranslateDBError(err)
	}
	return count, nil
}

// CountByStatus 统计指定状态的菜单数量
func (r *menuRepository) CountByStatus(ctx context.Context, tenantID string, status valueobject.MenuStatus) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&entity.Menu{}).
		Where("tenant_id = ? AND status = ?", tenantID, status).
		Count(&count).Error; err != nil {
		return 0, database.TranslateDBError(err)
	}
	return count, nil
}

// ExistsByName 检查菜单名称是否存在
func (r *menuRepository) ExistsByName(ctx context.Context, tenantID, name string) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&entity.Menu{}).
		Where("tenant_id = ? AND name = ?", tenantID, name).
		Count(&count).Error; err != nil {
		return false, database.TranslateDBError(err)
	}
	return count > 0, nil
}

// ExistsByPath 检查菜单路径是否存在
func (r *menuRepository) ExistsByPath(ctx context.Context, tenantID, path string) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&entity.Menu{}).
		Where("tenant_id = ? AND path = ?", tenantID, path).
		Count(&count).Error; err != nil {
		return false, database.TranslateDBError(err)
	}
	return count > 0, nil
}

// ExistsByNameExcludeID 检查菜单名称是否存在（排除指定ID）
func (r *menuRepository) ExistsByNameExcludeID(ctx context.Context, tenantID, name, excludeID string) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&entity.Menu{}).
		Where("tenant_id = ? AND name = ? AND business_id != ?", tenantID, name, excludeID).
		Count(&count).Error; err != nil {
		return false, database.TranslateDBError(err)
	}
	return count > 0, nil
}

// ExistsByPathExcludeID 检查菜单路径是否存在（排除指定ID）
func (r *menuRepository) ExistsByPathExcludeID(ctx context.Context, tenantID, path, excludeID string) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&entity.Menu{}).
		Where("tenant_id = ? AND path = ? AND business_id != ?", tenantID, path, excludeID).
		Count(&count).Error; err != nil {
		return false, database.TranslateDBError(err)
	}
	return count > 0, nil
}

// FindByExactPath 根据精确路径查找菜单
func (r *menuRepository) FindByExactPath(ctx context.Context, tenantID, path string) (*entity.Menu, error) {
	var menu entity.Menu
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND path = ?", tenantID, path).
		First(&menu).Error; err != nil {
		return nil, database.TranslateDBError(err)
	}
	return &menu, nil
}

// FindByPathPrefix 根据路径前缀查找菜单
func (r *menuRepository) FindByPathPrefix(ctx context.Context, tenantID, pathPrefix string) ([]*entity.Menu, error) {
	var menus []*entity.Menu
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND path LIKE ?", tenantID, pathPrefix+"%").
		Order("path ASC").
		Find(&menus).Error; err != nil {
		return nil, database.TranslateDBError(err)
	}
	return menus, nil
}

// UpdateStatus 更新菜单状态
func (r *menuRepository) UpdateStatus(ctx context.Context, id string, status valueobject.MenuStatus) error {
	result := r.db.WithContext(ctx).
		Model(&entity.Menu{}).
		Where("business_id = ?", id).
		Update("status", status)

	if result.Error != nil {
		return database.TranslateDBError(result.Error)
	}

	if result.RowsAffected == 0 {
		return database.TranslateDBError(gorm.ErrRecordNotFound)
	}

	return nil
}

// UpdateVisibility 更新菜单可见性
func (r *menuRepository) UpdateVisibility(ctx context.Context, id string, visible bool) error {
	result := r.db.WithContext(ctx).
		Model(&entity.Menu{}).
		Where("business_id = ?", id).
		Update("is_visible", visible)

	if result.Error != nil {
		return database.TranslateDBError(result.Error)
	}

	if result.RowsAffected == 0 {
		return database.TranslateDBError(gorm.ErrRecordNotFound)
	}

	return nil
}

// BatchUpdateStatus 批量更新菜单状态
func (r *menuRepository) BatchUpdateStatus(ctx context.Context, ids []string, status valueobject.MenuStatus) error {
	if len(ids) == 0 {
		return nil
	}

	result := r.db.WithContext(ctx).
		Model(&entity.Menu{}).
		Where("business_id IN ?", ids).
		Update("status", status)

	if result.Error != nil {
		return database.TranslateDBError(result.Error)
	}

	return nil
}

// UpdateSort 更新菜单排序
func (r *menuRepository) UpdateSort(ctx context.Context, id string, sort int) error {
	result := r.db.WithContext(ctx).
		Model(&entity.Menu{}).
		Where("business_id = ?", id).
		Update("sort", sort)

	if result.Error != nil {
		return database.TranslateDBError(result.Error)
	}

	if result.RowsAffected == 0 {
		return database.TranslateDBError(gorm.ErrRecordNotFound)
	}

	return nil
}

// BatchUpdateSort 批量更新菜单排序
func (r *menuRepository) BatchUpdateSort(ctx context.Context, sortUpdates map[string]int) error {
	if len(sortUpdates) == 0 {
		return nil
	}

	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for id, sort := range sortUpdates {
			if err := tx.Model(&entity.Menu{}).
				Where("business_id = ?", id).
				Update("sort", sort).Error; err != nil {
				return database.TranslateDBError(err)
			}
		}
		return nil
	})
}

// GetMaxSortInParent 获取父菜单下的最大排序号
func (r *menuRepository) GetMaxSortInParent(ctx context.Context, tenantID, parentID string) (int, error) {
	var maxSort int
	query := r.db.WithContext(ctx).Model(&entity.Menu{}).
		Select("COALESCE(MAX(sort), 0)").
		Where("tenant_id = ?", tenantID)

	if parentID == "" {
		query = query.Where("parent_id IS NULL OR parent_id = ''")
	} else {
		query = query.Where("parent_id = ?", parentID)
	}

	if err := query.Scan(&maxSort).Error; err != nil {
		return 0, database.TranslateDBError(err)
	}

	return maxSort, nil
}

// UpdateParent 更新菜单父级和层级
func (r *menuRepository) UpdateParent(ctx context.Context, id, newParentID string, newLevel int) error {
	updates := map[string]interface{}{
		"parent_id": newParentID,
		"level":     newLevel,
	}

	result := r.db.WithContext(ctx).
		Model(&entity.Menu{}).
		Where("business_id = ?", id).
		Updates(updates)

	if result.Error != nil {
		return database.TranslateDBError(result.Error)
	}

	if result.RowsAffected == 0 {
		return database.TranslateDBError(gorm.ErrRecordNotFound)
	}

	return nil
}

// MoveToParent 移动菜单到新父级
func (r *menuRepository) MoveToParent(ctx context.Context, id, newParentID string) error {
	// 计算新层级
	var newLevel int
	if newParentID == "" {
		newLevel = 1
	} else {
		var parent entity.Menu
		if err := r.db.WithContext(ctx).
			Select("level").
			Where("business_id = ?", newParentID).
			First(&parent).Error; err != nil {
			return database.TranslateDBError(err)
		}
		newLevel = parent.Level + 1
	}

	return r.UpdateParent(ctx, id, newParentID, newLevel)
}

// UpdateLevel 更新菜单层级
func (r *menuRepository) UpdateLevel(ctx context.Context, id string, level int) error {
	result := r.db.WithContext(ctx).
		Model(&entity.Menu{}).
		Where("business_id = ?", id).
		Update("level", level)

	if result.Error != nil {
		return database.TranslateDBError(result.Error)
	}

	if result.RowsAffected == 0 {
		return database.TranslateDBError(gorm.ErrRecordNotFound)
	}

	return nil
}

// UpdateLevelRecursive 递归更新菜单层级
func (r *menuRepository) UpdateLevelRecursive(ctx context.Context, parentID string, startLevel int) error {
	// 使用递归CTE更新所有子菜单的层级
	query := `
		WITH RECURSIVE menu_levels AS (
			-- 基础查询：查找指定父菜单的直接子菜单
			SELECT business_id, ? as new_level FROM menus
			WHERE parent_id = ? AND deleted_at IS NULL

			UNION ALL

			-- 递归查询：查找子菜单的子菜单
			SELECT m.business_id, ml.new_level + 1 as new_level
			FROM menus m
			INNER JOIN menu_levels ml ON m.parent_id = ml.business_id
			WHERE m.deleted_at IS NULL
		)
		UPDATE menus SET level = menu_levels.new_level
		FROM menu_levels
		WHERE menus.business_id = menu_levels.business_id
	`

	if err := r.db.WithContext(ctx).Exec(query, startLevel, parentID).Error; err != nil {
		return database.TranslateDBError(err)
	}

	return nil
}
