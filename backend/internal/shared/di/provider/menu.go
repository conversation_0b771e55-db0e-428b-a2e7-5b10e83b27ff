package provider

import (
	"backend/internal/adapters/repository/postgres"
	"backend/internal/application/service"
	"backend/internal/domain/menu/repository"
	menuService "backend/internal/domain/menu/service"
	"backend/pkg/infrastructure/auth"
	"backend/pkg/infrastructure/logger"

	"gorm.io/gorm"
)

// ProvideMenuRepository 提供菜单仓储
func ProvideMenuRepository(db *gorm.DB, logger logger.Logger) repository.MenuRepository {
	return postgres.NewMenuRepository(db, logger)
}

// ProvideMenuDomainService 提供菜单领域服务
func ProvideMenuDomainService(
	menuRepo repository.MenuRepository,
	logger logger.Logger,
) menuService.MenuDomainService {
	return menuService.NewMenuDomainService(menuRepo, logger)
}

// ProvideRouteTreeService 提供路由树服务
func ProvideRouteTreeService(
	menuRepo repository.MenuRepository,
	menuDomainSvc menuService.MenuDomainService,
	casbinManager auth.CasbinManager,
	logger logger.Logger,
) service.RouteTreeService {
	return service.NewRouteTreeService(
		menuRepo,
		menuDomainSvc,
		casbinManager,
		logger,
	)
}
